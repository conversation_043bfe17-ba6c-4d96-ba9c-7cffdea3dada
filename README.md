# PDF and Image Processing Tool

Convert ZIP files containing images to PDF and extract text from images using OCR.

## Features

✅ **ZIP to PDF conversion** - Convert ZIP files containing images into a single PDF
⚠️ **OCR text extraction** - Extract text from images and PDFs (requires Tesseract)

## Quick Start

```bash
# Install dependencies
pip install -r requirements.txt

# Basic usage - convert ZIP to PDF
python pdf.py

# For OCR functionality, install Tesseract OCR first
# See INSTALL_TESSERACT.md for instructions
```

## Usage

### Convert ZIP to PDF
```python
from pdf import convert_zip_images_to_pdf
convert_zip_images_to_pdf("images.zip", "output.pdf")
```

### Extract text from images (requires Tesseract)
```python
from pdf import extract_text_from_image, extract_text_from_pdf_images

# From single image
text = extract_text_from_image("image.png", "output.txt")

# From PDF
text = extract_text_from_pdf_images("document.pdf", "extracted.txt")
```

## Installation

1. **Python dependencies**: `pip install -r requirements.txt`
2. **For OCR**: Install Tesseract OCR (see `INSTALL_TESSERACT.md`)

## Supported Formats

- **Images**: PNG, JPG, JPEG, BMP, TIFF, GIF
- **Input**: ZIP files, individual images, PDF files
- **Output**: PDF files, text files
