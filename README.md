# Contact Extraction Tool

🚀 **Intelligent Contact Information Extractor** - Convert ZIP files containing business card images or PDF documents into structured contact data with automatic Excel export.

## ✨ Features

✅ **ZIP to PDF Conversion** - Convert ZIP archives containing images into a single PDF document
✅ **Smart OCR Text Extraction** - Extract text from images and PDFs using Tesseract OCR
✅ **Intelligent Contact Parsing** - Automatically identify and extract contact information including:
- Names, job titles, and company names
- Email addresses and phone numbers (Mobile, Direct, HQ)
- Location and address information
✅ **Professional Excel Export** - Generate formatted Excel spreadsheets with extracted contacts
✅ **Multi-format Support** - Process both ZIP archives and PDF files
✅ **Text Cleaning & Normalization** - Remove OCR artifacts and standardize data

## 🚀 Quick Start

```bash
# Install Python dependencies
pip install pillow fpdf2 pytesseract PyMuPDF openpyxl

# Install Tesseract OCR (Windows)
# Download from: https://github.com/UB-Mannheim/tesseract/wiki
# Or use chocolatey: choco install tesseract

# Process a ZIP file containing business card images
python main.py "business_cards.zip"

# Process a PDF document
python main.py "document.pdf" "my_contacts.xlsx"
```

## 📖 Usage

### Command Line Interface
```bash
# Basic usage - auto-generate output filename
python main.py input_file.zip

# Specify custom output Excel filename
python main.py input_file.pdf custom_contacts.xlsx
```

### Python API
```python
from main import process_file, convert_zip_to_pdf, extract_text_from_pdf

# Process any supported file type
excel_file = process_file("business_cards.zip", "contacts.xlsx")

# Individual functions
convert_zip_to_pdf("images.zip", "output.pdf")
extract_text_from_pdf("document.pdf", "extracted_text.txt")
```

## 🛠️ Installation

### 1. Python Dependencies
```bash
pip install pillow fpdf2 pytesseract PyMuPDF openpyxl
```

### 2. Tesseract OCR Installation

**Windows:**
- Download installer from [UB Mannheim Tesseract](https://github.com/UB-Mannheim/tesseract/wiki)
- Or use Chocolatey: `choco install tesseract`
- Update the path in `main.py` if needed:
```python
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
```

**macOS:**
```bash
brew install tesseract
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get install tesseract-ocr
```

## 📁 Supported Formats

### Input Formats
- **ZIP Archives**: Containing PNG, JPG, JPEG, BMP, TIFF, GIF images
- **PDF Files**: Both text-based and image-based (scanned) PDFs
- **Image Files**: PNG, JPG, JPEG, BMP, TIFF, GIF

### Output Formats
- **Excel Files**: Professional formatted .xlsx with contact data
- **Text Files**: Raw extracted text for debugging
- **PDF Files**: Converted from ZIP archives (temporary)

## 🎯 How It Works

1. **File Processing**: Accepts ZIP archives or PDF files as input
2. **Image Extraction**: For ZIP files, extracts all images and converts to PDF
3. **OCR Processing**: Uses Tesseract to extract text from images/scanned PDFs
4. **Smart Parsing**: Applies intelligent algorithms to identify contact information:
   - **Name Detection**: Identifies person names vs. company names
   - **Title Recognition**: Finds job titles using keyword matching
   - **Company Extraction**: Detects company names and organizations
   - **Contact Details**: Extracts emails, phone numbers, and locations
5. **Data Cleaning**: Removes OCR artifacts and normalizes text
6. **Excel Export**: Creates professionally formatted spreadsheet with all contacts

## 📊 Output Structure

The generated Excel file contains the following columns:
- **Name**: Person's full name
- **Title**: Job title or position
- **Company**: Company or organization name
- **Email**: Email address
- **Mobile Phone**: Mobile/cell phone number
- **Direct Phone**: Direct office line
- **HQ Phone**: Headquarters phone number
- **Location**: Address or location information

## 🔧 Configuration

### Tesseract Path Configuration
If Tesseract is not in your system PATH, update the path in `main.py`:
```python
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
```

### Customizing Contact Extraction
You can modify the extraction patterns in `main.py`:
- **Title Keywords**: Add job titles to recognize in `title_keywords` list
- **Company Indicators**: Add company name patterns in `company_indicators` list
- **Phone Patterns**: Modify regex patterns for different phone number formats
- **Location Parsing**: Adjust location detection logic

## 🚨 Troubleshooting

### Common Issues

**"Tesseract not found" Error:**
- Ensure Tesseract is installed and in PATH
- Update `tesseract_cmd` path in `main.py`
- Restart terminal/IDE after installation

**Poor OCR Results:**
- Ensure images are high resolution and clear
- Check image orientation (rotate if needed)
- Verify good contrast between text and background

**Missing Contact Information:**
- Review the extracted text file to see raw OCR output
- Adjust extraction patterns for your specific document format
- Consider preprocessing images for better OCR results

## 📝 Example Output

```
📦 Processing ZIP file: business_cards.zip
Step 1: Converting ZIP to PDF...
Step 2: Extracting text from PDF...
✓ Text saved: business_cards_text.txt
Step 2: Extracting contact information...
✓ Found 15 contacts
Step 3: Creating Excel file...
✓ Excel file created: business_cards_contacts.xlsx

📊 Summary:
   • Input file: business_cards.zip
   • Text file: business_cards_text.txt
   • Excel file: business_cards_contacts.xlsx
   • Contacts extracted: 15
```

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve the tool!

## 📄 License

This project is open source. Feel free to use and modify as needed.
